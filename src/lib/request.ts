/**
 * 原生HTTP请求封装
 * 支持GET和POST请求，基于fetch API
 */

// 请求配置接口
export interface RequestConfig {
  url: string;
  method?: 'GET' | 'POST';
  headers?: Record<string, string>;
  timeout?: number;
  data?: any;
  params?: Record<string, any>;
}

// 响应接口
export interface RequestResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Headers;
}

// 请求错误类
export class RequestError extends Error {
  public status?: number;
  public statusText?: string;
  public response?: Response;

  constructor(message: string, status?: number, statusText?: string, response?: Response) {
    super(message);
    this.name = 'RequestError';
    this.status = status;
    this.statusText = statusText;
    this.response = response;
  }
}

// 默认配置
const DEFAULT_CONFIG = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

/**
 * 将对象转换为URL查询参数
 */
function objectToQueryString(params: Record<string, any>): string {
  const searchParams = new URLSearchParams();
  
  Object.keys(params).forEach(key => {
    const value = params[key];
    if (value !== null && value !== undefined) {
      searchParams.append(key, String(value));
    }
  });
  
  return searchParams.toString();
}

/**
 * 创建带超时的fetch请求
 */
function fetchWithTimeout(url: string, options: RequestInit, timeout: number): Promise<Response> {
  return new Promise((resolve, reject) => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
      reject(new RequestError(`Request timeout after ${timeout}ms`, 0, 'Timeout'));
    }, timeout);

    fetch(url, {
      ...options,
      signal: controller.signal,
    })
      .then(response => {
        clearTimeout(timeoutId);
        resolve(response);
      })
      .catch(error => {
        clearTimeout(timeoutId);
        if (error.name === 'AbortError') {
          reject(new RequestError(`Request timeout after ${timeout}ms`, 0, 'Timeout'));
        } else {
          reject(error);
        }
      });
  });
}

/**
 * 通用请求方法
 */
async function request<T = any>(config: RequestConfig): Promise<RequestResponse<T>> {
  const {
    url,
    method = 'GET',
    headers = {},
    timeout = DEFAULT_CONFIG.timeout,
    data,
    params,
  } = config;

  // 构建完整URL
  let fullUrl = url;
  if (params && Object.keys(params).length > 0) {
    const queryString = objectToQueryString(params);
    fullUrl += (url.includes('?') ? '&' : '?') + queryString;
  }

  // 构建请求选项
  const fetchOptions: RequestInit = {
    method,
    headers: {
      ...DEFAULT_CONFIG.headers,
      ...headers,
    },
  };

  // 添加请求体（仅对POST等方法）
  if (data && method !== 'GET') {
    if (typeof data === 'object') {
      fetchOptions.body = JSON.stringify(data);
    } else {
      fetchOptions.body = data;
    }
  }

  try {
    // 发送请求
    const response = await fetchWithTimeout(fullUrl, fetchOptions, timeout);

    // 检查响应状态
    if (!response.ok) {
      throw new RequestError(
        `HTTP Error: ${response.status} ${response.statusText}`,
        response.status,
        response.statusText,
        response
      );
    }

    // 解析响应数据
    let responseData: T;
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      responseData = await response.json();
    } else {
      responseData = await response.text() as unknown as T;
    }

    return {
      data: responseData,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    };
  } catch (error) {
    if (error instanceof RequestError) {
      throw error;
    }
    throw new RequestError(
      error instanceof Error ? error.message : 'Unknown error occurred',
      0,
      'Network Error'
    );
  }
}

/**
 * GET请求
 */
export function get<T = any>(
  url: string,
  params?: Record<string, any>,
  config?: Omit<RequestConfig, 'url' | 'method' | 'params'>
): Promise<RequestResponse<T>> {
  return request<T>({
    url,
    method: 'GET',
    params,
    ...config,
  });
}

/**
 * POST请求
 */
export function post<T = any>(
  url: string,
  data?: any,
  config?: Omit<RequestConfig, 'url' | 'method' | 'data'>
): Promise<RequestResponse<T>> {
  return request<T>({
    url,
    method: 'POST',
    data,
    ...config,
  });
}

// 导出默认实例
export default {
  request,
  get,
  post,
  RequestError,
};
