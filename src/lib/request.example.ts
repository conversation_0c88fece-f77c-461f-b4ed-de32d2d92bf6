/**
 * Request库使用示例
 */

import { get, post, RequestError } from './request';

// 示例：GET请求
export async function getUserList() {
  try {
    const response = await get('/api/users', {
      page: 1,
      limit: 10,
    });
    
    console.log('用户列表:', response.data);
    return response.data;
  } catch (error) {
    if (error instanceof RequestError) {
      console.error('请求失败:', error.message, error.status);
    } else {
      console.error('未知错误:', error);
    }
    throw error;
  }
}

// 示例：POST请求
export async function createUser(userData: any) {
  try {
    const response = await post('/api/users', userData, {
      headers: {
        'Authorization': 'Bearer your-token-here',
      },
    });
    
    console.log('用户创建成功:', response.data);
    return response.data;
  } catch (error) {
    if (error instanceof RequestError) {
      console.error('创建用户失败:', error.message, error.status);
    } else {
      console.error('未知错误:', error);
    }
    throw error;
  }
}

// 示例：带自定义配置的请求
export async function uploadFile(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  
  try {
    const response = await post('/api/upload', formData, {
      headers: {
        // 不设置Content-Type，让浏览器自动设置multipart/form-data
      },
      timeout: 30000, // 30秒超时
    });
    
    console.log('文件上传成功:', response.data);
    return response.data;
  } catch (error) {
    if (error instanceof RequestError) {
      console.error('文件上传失败:', error.message, error.status);
    } else {
      console.error('未知错误:', error);
    }
    throw error;
  }
}

// 示例：处理不同类型的响应
export async function downloadFile(fileId: string) {
  try {
    const response = await get(`/api/files/${fileId}/download`, undefined, {
      headers: {
        'Accept': 'application/octet-stream',
      },
    });
    
    // 如果是文件下载，response.data 可能是 blob 或 text
    console.log('文件下载成功');
    return response.data;
  } catch (error) {
    if (error instanceof RequestError) {
      console.error('文件下载失败:', error.message, error.status);
    } else {
      console.error('未知错误:', error);
    }
    throw error;
  }
}
