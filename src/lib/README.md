# Request 请求库

基于原生 fetch API 封装的 HTTP 请求库，支持 TypeScript，提供完整的类型定义。

## 特性

- 🚀 基于原生 fetch API，无额外依赖
- 📝 完整的 TypeScript 类型支持
- ⏱️ 支持请求超时设置
- 🔧 灵活的配置选项
- 📊 统一的错误处理
- 🎯 支持 GET 和 POST 请求
- 📋 自动处理查询参数和请求体

## 安装使用

```typescript
// 导入方式1：按需导入
import { get, post, RequestError } from '@/lib/request';

// 导入方式2：默认导入
import request from '@/lib/request';

// 导入方式3：从lib统一入口导入
import { get, post } from '@/lib';
```

## 基本用法

### GET 请求

```typescript
// 简单GET请求
const response = await get('/api/users');
console.log(response.data);

// 带查询参数的GET请求
const response = await get('/api/users', {
  page: 1,
  limit: 10,
  status: 'active'
});

// 带自定义配置的GET请求
const response = await get('/api/users', { page: 1 }, {
  headers: {
    'Authorization': 'Bearer token'
  },
  timeout: 5000
});
```

### POST 请求

```typescript
// 简单POST请求
const response = await post('/api/users', {
  name: 'John Doe',
  email: '<EMAIL>'
});

// 带自定义配置的POST请求
const response = await post('/api/users', userData, {
  headers: {
    'Authorization': 'Bearer token',
    'X-Custom-Header': 'value'
  },
  timeout: 10000
});

// 上传文件
const formData = new FormData();
formData.append('file', file);
const response = await post('/api/upload', formData, {
  headers: {
    // 不设置Content-Type，让浏览器自动设置
  }
});
```

## 错误处理

```typescript
import { get, RequestError } from '@/lib/request';

try {
  const response = await get('/api/users');
  console.log(response.data);
} catch (error) {
  if (error instanceof RequestError) {
    console.error('请求失败:', {
      message: error.message,
      status: error.status,
      statusText: error.statusText
    });
  } else {
    console.error('未知错误:', error);
  }
}
```

## 配置选项

### RequestConfig 接口

```typescript
interface RequestConfig {
  url: string;                    // 请求URL
  method?: 'GET' | 'POST';       // 请求方法
  headers?: Record<string, string>; // 请求头
  timeout?: number;              // 超时时间（毫秒）
  data?: any;                    // 请求体数据
  params?: Record<string, any>;  // 查询参数
}
```

### 默认配置

```typescript
const DEFAULT_CONFIG = {
  timeout: 10000,  // 10秒超时
  headers: {
    'Content-Type': 'application/json',
  },
};
```

## 响应格式

```typescript
interface RequestResponse<T = any> {
  data: T;           // 响应数据
  status: number;    // HTTP状态码
  statusText: string; // HTTP状态文本
  headers: Headers;  // 响应头
}
```

## 高级用法

### 使用通用request方法

```typescript
import { request } from '@/lib/request';

const response = await request({
  url: '/api/users',
  method: 'POST',
  data: { name: 'John' },
  params: { include: 'profile' },
  headers: { 'Authorization': 'Bearer token' },
  timeout: 15000
});
```

### 类型安全

```typescript
interface User {
  id: number;
  name: string;
  email: string;
}

// 指定响应数据类型
const response = await get<User[]>('/api/users');
// response.data 的类型为 User[]

const userResponse = await post<User>('/api/users', userData);
// userResponse.data 的类型为 User
```

## 注意事项

1. 请求超时默认为10秒，可以通过 `timeout` 参数自定义
2. 默认请求头包含 `Content-Type: application/json`
3. GET请求的 `params` 会自动转换为URL查询参数
4. POST请求的 `data` 会自动序列化为JSON（除非是FormData等特殊类型）
5. 响应会根据 `Content-Type` 自动解析为JSON或文本
6. 所有HTTP错误状态码都会抛出 `RequestError` 异常
